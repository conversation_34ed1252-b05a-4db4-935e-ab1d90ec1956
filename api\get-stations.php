<?php
// تضمين ملف الإعدادات
require_once '../webset.php';

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// التحقق من وجود معرف المدينة
if (!isset($_GET['city_id']) || empty($_GET['city_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'معرف المدينة مطلوب',
        'stations' => []
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

$city_id = intval($_GET['city_id']);

try {
    // جلب المحطات للمدينة المحددة
    $stations = getAllFrom('*', 'bus_stations', 'WHERE city_id = "'.$city_id.'" AND status = 1', 'ORDER BY station_name ASC');
    
    if (count($stations) > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'تم جلب المحطات بنجاح',
            'stations' => $stations
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'لا توجد محطات متاحة لهذه المدينة',
            'stations' => []
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'stations' => []
    ], JSON_UNESCAPED_UNICODE);
}
?>
