<?php
$Title_page = 'نتائج البحث - رحلات الباصات';

// التحقق من البيانات المرسلة
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: " . $Site_URL . "/bus-booking");
    exit();
}

$from_city = intval($_POST['from_city']);
$from_station = intval($_POST['from_station']);
$to_station = intval($_POST['to_station']);
$travel_date = $_POST['travel_date'];
$seat_count = intval($_POST['seat_count']);

// التحقق من صحة البيانات
if (!$from_city || !$from_station || !$to_station || !$travel_date || !$seat_count) {
    header("Location: " . $Site_URL . "/bus-booking");
    exit();
}

// التحقق من أن التاريخ ليس في الماضي
if (strtotime($travel_date) < strtotime(date('Y-m-d'))) {
    header("Location: " . $Site_URL . "/bus-booking");
    exit();
}

// جلب معلومات المحطات
$from_station_info = getAllFrom('bs.*, c.name as city_name', 'bus_stations bs LEFT JOIN cities c ON bs.city_id = c.id', 'WHERE bs.id = "'.$from_station.'"', '')[0];
$to_station_info = getAllFrom('*', 'bus_stations', 'WHERE id = "'.$to_station.'"', '')[0];

// البحث عن الرحلات المتاحة
$search_query = "
    SELECT 
        bs.*,
        br.route_name,
        br.bus_type,
        br.duration_hours,
        fs.station_name as from_station_name,
        fs.station_address as from_station_address,
        ts.station_name as to_station_name,
        ts.station_address as to_station_address,
        c.name as from_city_name
    FROM bus_schedules bs
    JOIN bus_routes br ON bs.route_id = br.id
    JOIN bus_stations fs ON br.from_station_id = fs.id
    JOIN bus_stations ts ON br.to_station_id = ts.id
    LEFT JOIN cities c ON fs.city_id = c.id
    WHERE br.from_station_id = '".$from_station."' 
    AND br.to_station_id = '".$to_station."'
    AND bs.departure_date = '".$travel_date."'
    AND bs.available_seats >= '".$seat_count."'
    AND bs.status = 'scheduled'
    AND br.status = 1
    ORDER BY bs.departure_time ASC
";

$available_trips = getAllFromQuery($search_query);

// جلب التواريخ البديلة (4 أيام تالية)
$alternative_dates = [];
for ($i = 1; $i <= 4; $i++) {
    $alt_date = date('Y-m-d', strtotime($travel_date . ' +' . $i . ' days'));
    $alt_query = "
        SELECT COUNT(*) as trip_count
        FROM bus_schedules bs
        JOIN bus_routes br ON bs.route_id = br.id
        WHERE br.from_station_id = '".$from_station."' 
        AND br.to_station_id = '".$to_station."'
        AND bs.departure_date = '".$alt_date."'
        AND bs.available_seats >= '".$seat_count."'
        AND bs.status = 'scheduled'
        AND br.status = 1
    ";
    $alt_result = getAllFromQuery($alt_query);
    $alternative_dates[] = [
        'date' => $alt_date,
        'formatted_date' => date('d/m/Y', strtotime($alt_date)),
        'day_name' => date('l', strtotime($alt_date)),
        'trip_count' => $alt_result[0]['trip_count']
    ];
}

include 'header.php';
?>

<style>
.search-results-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.search-summary {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.search-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.route-info {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
}

.route-arrow {
    color: #3498db;
    font-size: 24px;
}

.search-details {
    color: #7f8c8d;
    font-size: 14px;
}

.alternative-dates {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.date-tabs {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.date-tab {
    min-width: 120px;
    padding: 12px 15px;
    background: #ecf0f1;
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    color: #2c3e50;
}

.date-tab.active {
    background: #3498db;
    border-color: #2980b9;
    color: white;
}

.date-tab:hover {
    background: #3498db;
    border-color: #2980b9;
    color: white;
    text-decoration: none;
}

.trips-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.trip-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.trip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.trip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.trip-time {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
}

.trip-duration {
    color: #7f8c8d;
    font-size: 14px;
}

.bus-type {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.bus-type.standard {
    background: #e8f5e8;
    color: #27ae60;
}

.bus-type.vip {
    background: #fff3e0;
    color: #f39c12;
}

.trip-details {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    align-items: center;
    margin-bottom: 20px;
}

.station-info {
    text-align: center;
}

.station-name {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.station-address {
    color: #7f8c8d;
    font-size: 12px;
}

.trip-arrow {
    text-align: center;
    color: #3498db;
    font-size: 30px;
}

.trip-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.price-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.price {
    font-size: 24px;
    font-weight: bold;
    color: #27ae60;
}

.seats-available {
    color: #7f8c8d;
    font-size: 14px;
}

.btn-book {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-book:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.no-trips {
    background: white;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.no-trips h3 {
    color: #e74c3c;
    margin-bottom: 15px;
}

.no-trips p {
    color: #7f8c8d;
    margin-bottom: 20px;
}

.btn-back {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
}

.btn-back:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

@media (max-width: 768px) {
    .search-info {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .trip-details {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .trip-arrow {
        transform: rotate(90deg);
    }
    
    .trip-footer {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn-book, .btn-back {
        width: 100%;
        text-align: center;
    }
}
</style>

<div class="search-results-container">
    <!-- ملخص البحث -->
    <div class="search-summary">
        <div class="search-info">
            <div class="route-info">
                <span><?php echo htmlspecialchars($from_station_info['city_name']); ?></span>
                <span class="route-arrow">✈️</span>
                <span>مكة المكرمة</span>
            </div>
            <div class="search-details">
                📅 <?php echo date('d/m/Y', strtotime($travel_date)); ?> | 
                👥 <?php echo $seat_count; ?> <?php echo $seat_count == 1 ? 'مقعد' : 'مقاعد'; ?>
            </div>
        </div>
    </div>

    <!-- التواريخ البديلة -->
    <div class="alternative-dates">
        <h4 style="margin-bottom: 15px; color: #2c3e50;">📅 تواريخ أخرى مقترحة</h4>
        <div class="date-tabs">
            <div class="date-tab active">
                <div style="font-weight: bold;"><?php echo date('d/m', strtotime($travel_date)); ?></div>
                <div style="font-size: 12px;">اليوم المحدد</div>
            </div>
            <?php foreach($alternative_dates as $alt_date): ?>
                <a href="<?php echo $Site_URL; ?>/bus-booking/<?php echo urlencode(json_encode([
                    'from_city' => $from_city,
                    'from_station' => $from_station,
                    'to_station' => $to_station,
                    'travel_date' => $alt_date['date'],
                    'seat_count' => $seat_count
                ])); ?>" class="date-tab">
                    <div style="font-weight: bold;"><?php echo date('d/m', strtotime($alt_date['date'])); ?></div>
                    <div style="font-size: 12px;"><?php echo $alt_date['trip_count']; ?> رحلة</div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- الرحلات المتاحة -->
    <?php if (count($available_trips) > 0): ?>
        <div class="trips-container">
            <?php foreach($available_trips as $trip): ?>
                <div class="trip-card">
                    <div class="trip-header">
                        <div>
                            <div class="trip-time">
                                <?php echo date('H:i', strtotime($trip['departure_time'])); ?> - 
                                <?php echo date('H:i', strtotime($trip['arrival_time'])); ?>
                            </div>
                            <div class="trip-duration">
                                مدة الرحلة: <?php echo $trip['duration_hours']; ?> ساعة
                            </div>
                        </div>
                        <div class="bus-type <?php echo $trip['bus_type']; ?>">
                            <?php echo $trip['bus_type'] == 'vip' ? 'VIP' : 'عادي'; ?>
                        </div>
                    </div>

                    <div class="trip-details">
                        <div class="station-info">
                            <div class="station-name"><?php echo htmlspecialchars($trip['from_station_name']); ?></div>
                            <div class="station-address"><?php echo htmlspecialchars($trip['from_station_address']); ?></div>
                        </div>
                        <div class="trip-arrow">🚌</div>
                        <div class="station-info">
                            <div class="station-name"><?php echo htmlspecialchars($trip['to_station_name']); ?></div>
                            <div class="station-address"><?php echo htmlspecialchars($trip['to_station_address']); ?></div>
                        </div>
                    </div>

                    <div class="trip-footer">
                        <div class="price-info">
                            <div class="price"><?php echo number_format($trip['price'] * $seat_count, 2); ?> ريال</div>
                            <div class="seats-available">
                                متاح <?php echo $trip['available_seats']; ?> مقعد | 
                                رقم الباص: <?php echo htmlspecialchars($trip['bus_number']); ?>
                            </div>
                        </div>
                        <a href="<?php echo $Site_URL; ?>/bus-booking-confirm/<?php echo urlencode(json_encode([
                            'schedule_id' => $trip['id'],
                            'seat_count' => $seat_count,
                            'total_price' => $trip['price'] * $seat_count,
                            'from_station_name' => $trip['from_station_name'],
                            'to_station_name' => $trip['to_station_name'],
                            'departure_date' => $trip['departure_date'],
                            'departure_time' => $trip['departure_time'],
                            'arrival_time' => $trip['arrival_time'],
                            'bus_type' => $trip['bus_type'],
                            'bus_number' => $trip['bus_number'],
                            'route_name' => $trip['route_name'],
                            'price_per_seat' => $trip['price']
                        ])); ?>" class="btn-book">
                            اختيار هذه الرحلة
                        </a>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="no-trips">
            <h3>😔 لا توجد رحلات متاحة</h3>
            <p>عذراً، لا توجد رحلات متاحة في التاريخ المحدد بالمواصفات المطلوبة.</p>
            <p>يرجى المحاولة مع تاريخ آخر أو تقليل عدد المقاعد المطلوبة.</p>
            <a href="<?php echo $Site_URL; ?>/bus-booking" class="btn-back">العودة للبحث</a>
        </div>
    <?php endif; ?>
</div>

<?php include 'footer.php'; ?>
