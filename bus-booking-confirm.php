<?php
$Title_page = 'تأكيد حجز الباص';

// فك تشفير بيانات الحجز
$booking_data = json_decode(urldecode($FURL[2]), true);

// التحقق من وجود البيانات
if (!$booking_data) {
    echo "<div style='padding: 20px; text-align: center;'>";
    echo "<h3>خطأ في بيانات الحجز</h3>";
    echo "<p>لم يتم العثور على بيانات الحجز أو أن البيانات تالفة.</p>";
    echo "<p><a href='".$Site_URL."/bus-booking' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة للبحث</a></p>";
    echo "</div>";
    exit();
}

// استخراج البيانات
$schedule_id = $booking_data['schedule_id'];
$seat_count = $booking_data['seat_count'];
$total_price = $booking_data['total_price'];

include 'header.php';
?>

<style>
.booking-confirm-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.booking-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.booking-title {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: bold;
}

.trip-summary {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.trip-route {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
}

.route-arrow {
    color: #3498db;
    font-size: 24px;
}

.trip-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #ecf0f1;
}

.detail-label {
    color: #7f8c8d;
    font-weight: bold;
}

.detail-value {
    color: #2c3e50;
    font-weight: bold;
}

.price-summary {
    background: #e8f5e8;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.total-price {
    font-size: 24px;
    font-weight: bold;
    color: #27ae60;
    border-top: 2px solid #27ae60;
    padding-top: 15px;
    margin-top: 15px;
}

.passenger-form {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
}

.form-title {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 250px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #34495e;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-control.required {
    border-color: #e74c3c;
}

.payment-methods {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
}

.payment-option {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s;
}

.payment-option:hover {
    border-color: #3498db;
    background: #f0f8ff;
}

.payment-option.selected {
    border-color: #27ae60;
    background: #e8f5e8;
}

.payment-option input[type="radio"] {
    margin-left: 15px;
    transform: scale(1.2);
}

.btn-confirm {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    padding: 15px 40px;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
    margin-bottom: 15px;
}

.btn-confirm:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-back {
    background: #6c757d;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
    text-decoration: none;
    display: block;
    text-align: center;
}

.btn-back:hover {
    background: #5a6268;
    color: white;
    text-decoration: none;
}

.terms-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #fff3cd;
    border-radius: 8px;
}

.terms-checkbox input[type="checkbox"] {
    margin-left: 10px;
    transform: scale(1.2);
}

@media (max-width: 768px) {
    .trip-route {
        flex-direction: column;
        gap: 10px;
    }
    
    .trip-details-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .form-group {
        min-width: 100%;
    }
}
</style>

<div class="booking-confirm-container">
    <div class="booking-card">
        <h1 class="booking-title">🎫 تأكيد حجز الباص</h1>
        
        <!-- ملخص الرحلة -->
        <div class="trip-summary">
            <div class="trip-route">
                <span><?php echo htmlspecialchars($booking_data['from_station_name']); ?></span>
                <span class="route-arrow">🚌</span>
                <span><?php echo htmlspecialchars($booking_data['to_station_name']); ?></span>
            </div>
            
            <div class="trip-details-grid">
                <div class="detail-item">
                    <span class="detail-label">📅 تاريخ السفر:</span>
                    <span class="detail-value"><?php echo date('d/m/Y', strtotime($booking_data['departure_date'])); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">🕐 وقت الانطلاق:</span>
                    <span class="detail-value"><?php echo date('H:i', strtotime($booking_data['departure_time'])); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">🕐 وقت الوصول:</span>
                    <span class="detail-value"><?php echo date('H:i', strtotime($booking_data['arrival_time'])); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">🚌 نوع الباص:</span>
                    <span class="detail-value"><?php echo $booking_data['bus_type'] == 'vip' ? 'VIP' : 'عادي'; ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">🔢 رقم الباص:</span>
                    <span class="detail-value"><?php echo htmlspecialchars($booking_data['bus_number']); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">👥 عدد المقاعد:</span>
                    <span class="detail-value"><?php echo $seat_count; ?> <?php echo $seat_count == 1 ? 'مقعد' : 'مقاعد'; ?></span>
                </div>
            </div>
        </div>

        <!-- ملخص الأسعار -->
        <div class="price-summary">
            <h4 style="margin-bottom: 15px; color: #2c3e50;">💰 ملخص الأسعار</h4>
            <div class="price-row">
                <span>سعر المقعد الواحد:</span>
                <span><?php echo number_format($booking_data['price_per_seat'], 2); ?> ريال</span>
            </div>
            <div class="price-row">
                <span>عدد المقاعد:</span>
                <span><?php echo $seat_count; ?></span>
            </div>
            <div class="price-row total-price">
                <span>إجمالي المبلغ:</span>
                <span><?php echo number_format($total_price, 2); ?> ريال</span>
            </div>
        </div>

        <!-- نموذج بيانات المسافر -->
        <form id="bookingForm" method="POST" action="<?php echo $Site_URL; ?>/api/process-bus-booking.php">
            <input type="hidden" name="booking_data" value="<?php echo htmlspecialchars(json_encode($booking_data)); ?>">
            
            <div class="passenger-form">
                <h4 class="form-title">👤 بيانات المسافر الرئيسي</h4>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="passenger_name">الاسم الكامل *</label>
                        <input type="text" id="passenger_name" name="passenger_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="passenger_phone">رقم الهاتف *</label>
                        <input type="tel" id="passenger_phone" name="passenger_phone" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="passenger_email">البريد الإلكتروني</label>
                        <input type="email" id="passenger_email" name="passenger_email" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="passenger_id">رقم الهوية</label>
                        <input type="text" id="passenger_id" name="passenger_id" class="form-control">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="special_requests">طلبات خاصة</label>
                    <textarea id="special_requests" name="special_requests" class="form-control" rows="3" placeholder="أي طلبات خاصة أو ملاحظات..."></textarea>
                </div>
            </div>

            <!-- طرق الدفع -->
            <div class="payment-methods">
                <h4 class="form-title">💳 طريقة الدفع</h4>
                
                <div class="payment-option selected" onclick="selectPayment(this, 'cash')">
                    <input type="radio" name="payment_method" value="cash" checked>
                    <div>
                        <strong>💵 الدفع عند الوصول</strong>
                        <p style="margin: 5px 0 0 0; color: #7f8c8d; font-size: 14px;">ادفع نقداً عند وصولك للمحطة</p>
                    </div>
                </div>
                
                <div class="payment-option" onclick="selectPayment(this, 'bank_transfer')">
                    <input type="radio" name="payment_method" value="bank_transfer">
                    <div>
                        <strong>🏦 تحويل بنكي</strong>
                        <p style="margin: 5px 0 0 0; color: #7f8c8d; font-size: 14px;">تحويل إلى حساب الشركة</p>
                    </div>
                </div>
            </div>

            <!-- الموافقة على الشروط -->
            <div class="terms-checkbox">
                <input type="checkbox" id="accept_terms" name="accept_terms" required>
                <label for="accept_terms">أوافق على <a href="#" style="color: #3498db;">الشروط والأحكام</a> وسياسة الإلغاء</label>
            </div>

            <button type="submit" class="btn-confirm">
                ✅ تأكيد الحجز
            </button>
            
            <a href="javascript:history.back()" class="btn-back">
                ← العودة للخلف
            </a>
        </form>
    </div>
</div>

<script>
function selectPayment(element, method) {
    // إزالة التحديد من جميع الخيارات
    document.querySelectorAll('.payment-option').forEach(option => {
        option.classList.remove('selected');
    });
    
    // إضافة التحديد للخيار المحدد
    element.classList.add('selected');
    
    // تحديد الراديو بوتن
    element.querySelector('input[type="radio"]').checked = true;
}

// التحقق من صحة النموذج
document.getElementById('bookingForm').addEventListener('submit', function(e) {
    const name = document.getElementById('passenger_name').value.trim();
    const phone = document.getElementById('passenger_phone').value.trim();
    const terms = document.getElementById('accept_terms').checked;
    
    if (!name || !phone || !terms) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة والموافقة على الشروط والأحكام');
        return false;
    }
    
    // التحقق من صحة رقم الهاتف
    const phoneRegex = /^[0-9+\-\s()]+$/;
    if (!phoneRegex.test(phone)) {
        e.preventDefault();
        alert('يرجى إدخال رقم هاتف صحيح');
        return false;
    }
    
    // تأكيد الحجز
    if (!confirm('هل أنت متأكد من تأكيد هذا الحجز؟')) {
        e.preventDefault();
        return false;
    }
});
</script>

<?php include 'footer.php'; ?>
