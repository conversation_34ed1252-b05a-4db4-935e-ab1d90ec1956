<?php
$thisthome = $MyCase == '' || $MyCase == '/' ? true : false;

if($thisthome){
  $headerClass= 'header-nav nav-homepage-style stricky main-menu';
  $logo1 = '<a class="header-logo logo1" href="'.$Site_URL.'"><img src="'.$Site_URL.'/'.$Logo_Light.'" alt="'.$Site_Name.'"></a>';
  $ovelay = '<div class="hiddenbar-body-ovelay"></div>';
}else{
  $headerClass= 'header-nav nav-innerpage-style bdrb1 main-menu';
  $logo1 = '';
  $ovelay ='';
}

$preloader = $Loader ? '<div class="preloader"></div>' : '';
  
echo '
<div class="wrapper ovh">
'.$preloader.'
<header class="'.$headerClass.'">
  <nav class="posr"> 
    <div class="container posr menu_bdrt1">
      <div class="row align-items-center justify-content-between">
        <div class="col-auto">
          <div class="d-flex align-items-center justify-content-between">
            <div class="logos ml40">
              '.$logo1.'
              <a class="header-logo logo2" href="'.$Site_URL.'"><img src="'.$Site_URL.'/'.$Logo.'" alt="'.$Site_Name.'"></a>
            </div>
            <!-- Responsive Menu Structure-->
            <ul id="respMenu" class="ace-responsive-menu" data-menu-style="horizontal">
              <li class="visible_list"> <a class="list-item" href="'.$Site_URL.'"><span class="title">'.tr('NAV_01').'</span></a> </li> 
              <li class="visible_list"> <a class="list-item" href="'.$Site_URL.'/cities"><span class="title">'.tr('NAV_02').'</span></a>
                <ul>
                ';
                for ($i=0; $i < count($AllCities) ; $i++) { 
                  echo '<li><a href="'.$Site_URL.'/'.$AllCities[$i]['link'].'">'.$AllCities[$i]['name'].'</a></li>';
                }
                echo ' 
                </ul>
              </li>
              <li class="visible_list"> <a class="list-item" href="'.$Site_URL.'/hotel"><span class="title">'.tr('NAV_03').'</span></a>
                <ul>
                  <li><a href="'.$Site_URL.'/hotel">'.tr('NAV_03_1').'</a></li>';
                  for ($i=0; $i < count($AllHotelCities) ; $i++) {
                    echo '<li><a href="'.$Site_URL.'/'.$AllHotelCities[$i]['slug'].'">'.$AllHotelCities[$i]['city_name'].'</a></li>';
                  }
                  echo '
                </ul>
              </li>
              <li class="visible_list"> <a class="list-item" href="'.$Site_URL.'/bus-booking"><span class="title">'.tr('NAV_07').'</span></a> </li>
              <li class="visible_list"> <a class="list-item" href="'.$Site_URL.'/privacy-policy"><span class="title">'.tr('NAV_04').'</span></a> </li> 
              <li class="visible_list"> <a class="list-item" href="'.$Site_URL.'/terms-and-conditions"><span class="title">'.tr('NAV_05').'</span></a> </li> 
              <li class="visible_list"> <a class="list-item" href="'.$Site_URL.'/contact-us"><span class="title">'.tr('NAV_06').'</span></a> </li> 
               <li class="visible_list"><a href="'.$Site_URL.'/signup" class="login_icon"><span class="fa-light fa-user-plus"></span></a> </li> 
               <li class="visible_list">   <a href="'.$Site_URL.'/login" class="login_icon"><span class="fa-light fa-user"></span></a> </li>
                <li class="visible_list">  <a href="#" class="login_icon"><span class="fa-solid fa-tag"></span></a> </li>
           </ul>
          </div>
        </div>
         
      </div>
    </div>
  </nav>
          
</header>
'.$ovelay.'

  <!-- Mobile Nav  -->
  <div id="page" class="mobilie_header_nav stylehome1">
    <div class="mobile-menu">
        <div class="header innerpage-style">
            <div class="menu_and_widgets">
                <div class="mobile_menu_bar d-flex justify-content-between align-items-center">
                    <a class="mobile_logo" href="'.$Site_URL.'"><img src="'.$Site_URL.'/'.$Logo.'" alt="'.$Site_Name.'"></a> 
                    <div class="menu_login_wrapper d-flex align-items-center">
                        <a href="#" class="login_icon"><span class="fa-solid fa-tag"></span></a> <!-- أيقونة تسجيل الدخول -->
                        <a href="'.$Site_URL.'/signup" class="login_icon"><span class="fa-light fa-user-plus"></span></a> 
                        <a href="'.$Site_URL.'/login" class="login_icon"><span class="fa-light fa-chalkboard-user"></span></a> 

                        <a class="menubar" href="#menu"><span class="fa-solid fa-list-ul"></span></a> <!-- القائمة -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /.mobile-menu -->
    <nav id="menu" class="">
        <ul>
            <li> <a href="'.$Site_URL.'">'.tr('NAV_01').'</a> </li> 
            <li> <a href="'.$Site_URL.'/cities">'.tr('NAV_02').'</a>
                <ul>
                ';
                for ($i=0; $i < count($AllCities) ; $i++) { 
                    echo '<li><a href="'.$Site_URL.'/'.$AllCities[$i]['link'].'">'.$AllCities[$i]['name'].'</a></li>';
                }
                echo ' 
                </ul>
            </li>
            <li> <a href="'.$Site_URL.'/hotel">'.tr('NAV_03').'</a>
                <ul>
                    <li><a href="'.$Site_URL.'/hotel">'.tr('NAV_03_1').'</a></li>';
                    for ($i=0; $i < count($AllHotelCities) ; $i++) {
                        echo '<li><a href="'.$Site_URL.'/'.$AllHotelCities[$i]['slug'].'">'.$AllHotelCities[$i]['city_name'].'</a></li>';
                    }
                    echo '
                </ul>
            </li>
            <li> <a href="'.$Site_URL.'/bus-booking">'.tr('NAV_07').'</a> </li>
            <li> <a href="'.$Site_URL.'/privacy-policy">'.tr('NAV_04').'</a> </li> 
            <li> <a href="'.$Site_URL.'/terms-and-conditions">'.tr('NAV_05').'</a> </li> 
            <li> <a href="'.$Site_URL.'/contact-us">'.tr('NAV_06').'</a> </li> 
        </ul>
    </nav>
</div>






';
if(isset($Title_page)){
   echo '
   <h1 class="d-none">'.$Title_page.'</h1>
   ';
}


