-- نظام حجز مقاعد الباصات - جداول قاعدة البيانات
-- تاريخ الإنشاء: 2025-07-29

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- --------------------------------------------------------

--
-- Table structure for table `bus_stations`
-- جدول المحطات
--

CREATE TABLE `bus_stations` (
  `id` int(11) NOT NULL,
  `city_id` int(11) NOT NULL COMMENT 'معرف المدينة من جدول cities',
  `station_name` varchar(255) NOT NULL COMMENT 'اسم المحطة',
  `station_address` text DEFAULT NULL COMMENT 'عنوان المحطة',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=نشط, 0=غير نشط',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `bus_stations`
--

INSERT INTO `bus_stations` (`id`, `city_id`, `station_name`, `station_address`, `status`, `created_at`, `updated_at`) VALUES
(1, 1, 'محطة البطحاء', 'حي البطحاء، الرياض', 1, NOW(), NOW()),
(2, 1, 'محطة الملك فهد', 'طريق الملك فهد، الرياض', 1, NOW(), NOW()),
(3, 1, 'محطة العليا', 'حي العليا، الرياض', 1, NOW(), NOW()),
(4, 3, 'محطة المدينة المركزية', 'وسط المدينة المنورة', 1, NOW(), NOW()),
(5, 3, 'محطة المسجد النبوي', 'بالقرب من المسجد النبوي', 1, NOW(), NOW()),
(6, 4, 'محطة الدمام المركزية', 'وسط مدينة الدمام', 1, NOW(), NOW()),
(7, 4, 'محطة الكورنيش', 'كورنيش الدمام', 1, NOW(), NOW()),
(8, 5, 'محطة الأحساء المركزية', 'وسط مدينة الأحساء', 1, NOW(), NOW()),
(9, 6, 'محطة جازان المركزية', 'وسط مدينة جازان', 1, NOW(), NOW()),
(10, 7, 'محطة القصيم المركزية', 'وسط مدينة القصيم', 1, NOW(), NOW()),
(11, 0, 'محطة مكة المركزية', 'وسط مكة المكرمة', 1, NOW(), NOW()),
(12, 0, 'محطة الحرم المكي', 'بالقرب من الحرم المكي', 1, NOW(), NOW());

-- --------------------------------------------------------

--
-- Table structure for table `bus_routes`
-- جدول الخطوط والمسارات
--

CREATE TABLE `bus_routes` (
  `id` int(11) NOT NULL,
  `route_name` varchar(255) NOT NULL COMMENT 'اسم الخط',
  `from_station_id` int(11) NOT NULL COMMENT 'محطة الانطلاق',
  `to_station_id` int(11) NOT NULL COMMENT 'محطة الوصول',
  `distance_km` decimal(6,2) DEFAULT NULL COMMENT 'المسافة بالكيلومتر',
  `duration_hours` decimal(4,2) DEFAULT NULL COMMENT 'مدة الرحلة بالساعات',
  `base_price` decimal(8,2) NOT NULL COMMENT 'السعر الأساسي',
  `bus_type` enum('standard','vip') NOT NULL DEFAULT 'standard' COMMENT 'نوع الباص',
  `total_seats` int(11) NOT NULL DEFAULT 45 COMMENT 'إجمالي المقاعد',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=نشط, 0=غير نشط',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `bus_routes`
--

INSERT INTO `bus_routes` (`id`, `route_name`, `from_station_id`, `to_station_id`, `distance_km`, `duration_hours`, `base_price`, `bus_type`, `total_seats`, `status`, `created_at`, `updated_at`) VALUES
(1, 'الرياض - مكة (عادي)', 1, 11, 870.00, 9.50, 120.00, 'standard', 45, 1, NOW(), NOW()),
(2, 'الرياض - مكة (VIP)', 2, 12, 870.00, 9.50, 200.00, 'vip', 30, 1, NOW(), NOW()),
(3, 'المدينة - مكة (عادي)', 4, 11, 450.00, 5.00, 80.00, 'standard', 45, 1, NOW(), NOW()),
(4, 'المدينة - مكة (VIP)', 5, 12, 450.00, 5.00, 150.00, 'vip', 30, 1, NOW(), NOW()),
(5, 'الدمام - مكة (عادي)', 6, 11, 1265.00, 13.00, 180.00, 'standard', 45, 1, NOW(), NOW()),
(6, 'الدمام - مكة (VIP)', 7, 12, 1265.00, 13.00, 280.00, 'vip', 30, 1, NOW(), NOW()),
(7, 'الأحساء - مكة (عادي)', 8, 11, 1150.00, 12.00, 160.00, 'standard', 45, 1, NOW(), NOW()),
(8, 'الأحساء - مكة (VIP)', 8, 12, 1150.00, 12.00, 250.00, 'vip', 30, 1, NOW(), NOW()),
(9, 'جازان - مكة (عادي)', 9, 11, 685.00, 7.50, 100.00, 'standard', 45, 1, NOW(), NOW()),
(10, 'جازان - مكة (VIP)', 9, 12, 685.00, 7.50, 180.00, 'vip', 30, 1, NOW(), NOW()),
(11, 'القصيم - مكة (عادي)', 10, 11, 650.00, 7.00, 95.00, 'standard', 45, 1, NOW(), NOW()),
(12, 'القصيم - مكة (VIP)', 10, 12, 650.00, 7.00, 170.00, 'vip', 30, 1, NOW(), NOW());

-- --------------------------------------------------------

--
-- Table structure for table `bus_schedules`
-- جدول جدولة الرحلات
--

CREATE TABLE `bus_schedules` (
  `id` int(11) NOT NULL,
  `route_id` int(11) NOT NULL COMMENT 'معرف الخط',
  `departure_date` date NOT NULL COMMENT 'تاريخ الانطلاق',
  `departure_time` time NOT NULL COMMENT 'وقت الانطلاق',
  `arrival_time` time NOT NULL COMMENT 'وقت الوصول',
  `available_seats` int(11) NOT NULL COMMENT 'المقاعد المتاحة',
  `price` decimal(8,2) NOT NULL COMMENT 'سعر المقعد',
  `bus_number` varchar(50) DEFAULT NULL COMMENT 'رقم الباص',
  `driver_name` varchar(255) DEFAULT NULL COMMENT 'اسم السائق',
  `driver_phone` varchar(20) DEFAULT NULL COMMENT 'هاتف السائق',
  `status` enum('scheduled','departed','arrived','cancelled') NOT NULL DEFAULT 'scheduled' COMMENT 'حالة الرحلة',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bus_bookings`
-- جدول حجوزات الباصات
--

CREATE TABLE `bus_bookings` (
  `id` int(11) NOT NULL,
  `booking_reference` varchar(20) NOT NULL COMMENT 'رقم الحجز المرجعي',
  `schedule_id` int(11) NOT NULL COMMENT 'معرف الرحلة المجدولة',
  `passenger_name` varchar(255) NOT NULL COMMENT 'اسم المسافر',
  `passenger_phone` varchar(20) NOT NULL COMMENT 'هاتف المسافر',
  `passenger_email` varchar(255) DEFAULT NULL COMMENT 'بريد المسافر',
  `passenger_id_number` varchar(20) DEFAULT NULL COMMENT 'رقم هوية المسافر',
  `seat_count` int(11) NOT NULL DEFAULT 1 COMMENT 'عدد المقاعد',
  `total_price` decimal(8,2) NOT NULL COMMENT 'إجمالي السعر',
  `booking_status` enum('pending','confirmed','cancelled','completed') NOT NULL DEFAULT 'pending' COMMENT 'حالة الحجز',
  `payment_status` enum('pending','paid','refunded') NOT NULL DEFAULT 'pending' COMMENT 'حالة الدفع',
  `payment_method` enum('cash','online','bank_transfer') DEFAULT 'cash' COMMENT 'طريقة الدفع',
  `special_requests` text DEFAULT NULL COMMENT 'طلبات خاصة',
  `created_by_ip` varchar(45) DEFAULT NULL COMMENT 'عنوان IP',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `bus_stations`
--
ALTER TABLE `bus_stations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `city_id` (`city_id`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `bus_routes`
--
ALTER TABLE `bus_routes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `from_station_id` (`from_station_id`),
  ADD KEY `to_station_id` (`to_station_id`),
  ADD KEY `status` (`status`),
  ADD KEY `bus_type` (`bus_type`);

--
-- Indexes for table `bus_schedules`
--
ALTER TABLE `bus_schedules`
  ADD PRIMARY KEY (`id`),
  ADD KEY `route_id` (`route_id`),
  ADD KEY `departure_date` (`departure_date`),
  ADD KEY `status` (`status`),
  ADD KEY `available_seats` (`available_seats`);

--
-- Indexes for table `bus_bookings`
--
ALTER TABLE `bus_bookings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `booking_reference` (`booking_reference`),
  ADD KEY `schedule_id` (`schedule_id`),
  ADD KEY `booking_status` (`booking_status`),
  ADD KEY `payment_status` (`payment_status`),
  ADD KEY `passenger_phone` (`passenger_phone`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `bus_stations`
--
ALTER TABLE `bus_stations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `bus_routes`
--
ALTER TABLE `bus_routes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `bus_schedules`
--
ALTER TABLE `bus_schedules`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `bus_bookings`
--
ALTER TABLE `bus_bookings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `bus_routes`
--
ALTER TABLE `bus_routes`
  ADD CONSTRAINT `bus_routes_ibfk_1` FOREIGN KEY (`from_station_id`) REFERENCES `bus_stations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `bus_routes_ibfk_2` FOREIGN KEY (`to_station_id`) REFERENCES `bus_stations` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `bus_schedules`
--
ALTER TABLE `bus_schedules`
  ADD CONSTRAINT `bus_schedules_ibfk_1` FOREIGN KEY (`route_id`) REFERENCES `bus_routes` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `bus_bookings`
--
ALTER TABLE `bus_bookings`
  ADD CONSTRAINT `bus_bookings_ibfk_1` FOREIGN KEY (`schedule_id`) REFERENCES `bus_schedules` (`id`) ON DELETE CASCADE;

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
