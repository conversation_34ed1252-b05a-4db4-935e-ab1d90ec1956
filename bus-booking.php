<?php
$Title_page = 'حجز مقاعد الباصات';

// التحقق من وجود البيانات المرسلة
$search_data = null;
if (isset($FURL[2]) && !empty($FURL[2])) {
    $search_data = json_decode(urldecode($FURL[2]), true);
}

// جلب جميع المدن النشطة
$cities = getAllFrom('*', 'cities', 'WHERE status = 1', 'ORDER BY name ASC');

// جلب محطات مكة (الوجهة الوحيدة المسموحة)
$mecca_stations = getAllFrom('*', 'bus_stations', 'WHERE city_id = 0 AND status = 1', 'ORDER BY station_name ASC');

include 'header.php';
?>

<style>
.bus-booking-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.booking-form-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.form-title {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: bold;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 250px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #34495e;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.btn-search {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 15px 40px;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
    margin-top: 20px;
}

.btn-search:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.important-note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
}

.important-note h4 {
    color: #856404;
    margin-bottom: 10px;
}

.important-note p {
    color: #856404;
    margin: 0;
    font-weight: bold;
}

.loading {
    display: none;
    text-align: center;
    padding: 20px;
}

.loading img {
    width: 50px;
    height: 50px;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }
    
    .form-group {
        min-width: 100%;
    }
    
    .bus-booking-container {
        padding: 10px;
    }
    
    .booking-form-card {
        padding: 20px;
    }
}
</style>

<div class="bus-booking-container">
    <div class="booking-form-card">
        <h1 class="form-title">🚌 حجز مقاعد الباصات</h1>
        
        <div class="important-note">
            <h4>⚠️ تنبيه مهم</h4>
            <p>نظام الحجز يعمل على حجز من وإلى مكة المكرمة فقط</p>
        </div>

        <form id="busSearchForm" method="POST" action="<?php echo $Site_URL; ?>/bus-search-results">
            <div class="form-row">
                <div class="form-group">
                    <label for="from_city">🏙️ السفر من المدينة</label>
                    <select id="from_city" name="from_city" class="form-control" required>
                        <option value="">اختر المدينة</option>
                        <?php foreach($cities as $city): ?>
                            <option value="<?php echo $city['id']; ?>" 
                                <?php echo (isset($search_data['from_city']) && $search_data['from_city'] == $city['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($city['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="from_station">🚏 السفر من المحطة</label>
                    <select id="from_station" name="from_station" class="form-control" required disabled>
                        <option value="">اختر المحطة</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="to_city">🕋 الوصول إلى مدينة</label>
                    <select id="to_city" name="to_city" class="form-control" readonly>
                        <option value="0" selected>مكة المكرمة</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="to_station">🚏 الوصول إلى المحطة</label>
                    <select id="to_station" name="to_station" class="form-control" required>
                        <option value="">اختر المحطة</option>
                        <?php foreach($mecca_stations as $station): ?>
                            <option value="<?php echo $station['id']; ?>"
                                <?php echo (isset($search_data['to_station']) && $search_data['to_station'] == $station['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($station['station_name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="travel_date">📅 تاريخ السفر</label>
                    <input type="date" id="travel_date" name="travel_date" class="form-control" 
                           min="<?php echo date('Y-m-d'); ?>" 
                           value="<?php echo isset($search_data['travel_date']) ? $search_data['travel_date'] : date('Y-m-d'); ?>" 
                           required>
                </div>

                <div class="form-group">
                    <label for="seat_count">👥 عدد المقاعد</label>
                    <select id="seat_count" name="seat_count" class="form-control" required>
                        <?php for($i = 1; $i <= 10; $i++): ?>
                            <option value="<?php echo $i; ?>" 
                                <?php echo (isset($search_data['seat_count']) && $search_data['seat_count'] == $i) ? 'selected' : ($i == 1 ? 'selected' : ''); ?>>
                                <?php echo $i; ?> <?php echo $i == 1 ? 'مقعد' : 'مقاعد'; ?>
                            </option>
                        <?php endfor; ?>
                    </select>
                </div>
            </div>

            <button type="submit" class="btn-search">
                🔍 عرض الرحلات المتاحة
            </button>
        </form>

        <div class="loading" id="loadingDiv">
            <p>جاري البحث عن الرحلات المتاحة...</p>
            <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        </div>
    </div>
</div>

<script>
// تحديث المحطات عند تغيير المدينة
document.getElementById('from_city').addEventListener('change', function() {
    const cityId = this.value;
    const stationSelect = document.getElementById('from_station');
    
    // إعادة تعيين المحطات
    stationSelect.innerHTML = '<option value="">اختر المحطة</option>';
    stationSelect.disabled = true;
    
    if (cityId) {
        // جلب المحطات عبر AJAX
        fetch('<?php echo $Site_URL; ?>/api/get-stations.php?city_id=' + cityId)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.stations.length > 0) {
                    data.stations.forEach(station => {
                        const option = document.createElement('option');
                        option.value = station.id;
                        option.textContent = station.station_name;
                        stationSelect.appendChild(option);
                    });
                    stationSelect.disabled = false;
                } else {
                    stationSelect.innerHTML = '<option value="">لا توجد محطات متاحة</option>';
                }
            })
            .catch(error => {
                console.error('خطأ في جلب المحطات:', error);
                stationSelect.innerHTML = '<option value="">خطأ في تحميل المحطات</option>';
            });
    }
});

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('busSearchForm').addEventListener('submit', function(e) {
    const fromCity = document.getElementById('from_city').value;
    const fromStation = document.getElementById('from_station').value;
    const toStation = document.getElementById('to_station').value;
    const travelDate = document.getElementById('travel_date').value;
    
    if (!fromCity || !fromStation || !toStation || !travelDate) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    // إظهار رسالة التحميل
    document.getElementById('loadingDiv').style.display = 'block';
});

// تعيين الحد الأدنى لتاريخ السفر (اليوم)
document.getElementById('travel_date').min = new Date().toISOString().split('T')[0];

// إضافة CSS للتحميل
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>

<?php include 'footer.php'; ?>
