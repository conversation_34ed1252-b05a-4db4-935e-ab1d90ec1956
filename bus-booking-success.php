<?php
$Title_page = 'تم تأكيد الحجز بنجاح';

// فك تشفير بيانات الحجز
$booking_data = json_decode(urldecode($FURL[2]), true);

// التحقق من وجود البيانات
if (!$booking_data || !isset($booking_data['booking_reference'])) {
    header("Location: " . $Site_URL . "/bus-booking");
    exit();
}

include 'header.php';
?>

<style>
.success-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.success-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    text-align: center;
    margin-bottom: 20px;
}

.success-icon {
    font-size: 80px;
    color: #27ae60;
    margin-bottom: 20px;
}

.success-title {
    color: #27ae60;
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 15px;
}

.success-message {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 30px;
    line-height: 1.6;
}

.booking-reference {
    background: #e8f5e8;
    border: 2px solid #27ae60;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: center;
}

.reference-number {
    font-size: 28px;
    font-weight: bold;
    color: #27ae60;
    margin-bottom: 10px;
}

.reference-note {
    color: #7f8c8d;
    font-size: 14px;
}

.booking-details {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: right;
}

.details-title {
    color: #2c3e50;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #e0e0e0;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    color: #7f8c8d;
    font-weight: bold;
}

.detail-value {
    color: #2c3e50;
    font-weight: bold;
}

.trip-route {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20px;
}

.route-arrow {
    color: #3498db;
    font-size: 24px;
}

.payment-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.payment-title {
    color: #856404;
    font-weight: bold;
    margin-bottom: 10px;
}

.payment-details {
    color: #856404;
    line-height: 1.6;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    color: white;
    text-decoration: none;
}

.important-notes {
    background: #e3f2fd;
    border: 1px solid #90caf9;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.notes-title {
    color: #1976d2;
    font-weight: bold;
    margin-bottom: 15px;
}

.notes-list {
    color: #1976d2;
    line-height: 1.8;
    text-align: right;
}

.notes-list li {
    margin-bottom: 8px;
}

@media (max-width: 768px) {
    .success-card {
        padding: 25px;
    }
    
    .success-icon {
        font-size: 60px;
    }
    
    .success-title {
        font-size: 24px;
    }
    
    .reference-number {
        font-size: 20px;
    }
    
    .trip-route {
        flex-direction: column;
        gap: 10px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
</style>

<div class="success-container">
    <div class="success-card">
        <div class="success-icon">✅</div>
        <h1 class="success-title">تم تأكيد حجزك بنجاح!</h1>
        <p class="success-message">
            شكراً لك على اختيار خدماتنا. تم تأكيد حجز مقاعد الباص بنجاح وسيتم التواصل معك قريباً.
        </p>
        
        <!-- رقم الحجز المرجعي -->
        <div class="booking-reference">
            <div class="reference-number"><?php echo htmlspecialchars($booking_data['booking_reference']); ?></div>
            <div class="reference-note">احتفظ بهذا الرقم المرجعي للمراجعة أو الاستفسار</div>
        </div>

        <!-- تفاصيل الحجز -->
        <div class="booking-details">
            <h3 class="details-title">📋 تفاصيل الحجز</h3>
            
            <div class="trip-route">
                <span><?php echo htmlspecialchars($booking_data['from_station_name']); ?></span>
                <span class="route-arrow">🚌</span>
                <span><?php echo htmlspecialchars($booking_data['to_station_name']); ?></span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">اسم المسافر:</span>
                <span class="detail-value"><?php echo htmlspecialchars($booking_data['passenger_name']); ?></span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">رقم الهاتف:</span>
                <span class="detail-value"><?php echo htmlspecialchars($booking_data['passenger_phone']); ?></span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">تاريخ السفر:</span>
                <span class="detail-value"><?php echo date('d/m/Y', strtotime($booking_data['departure_date'])); ?></span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">وقت الانطلاق:</span>
                <span class="detail-value"><?php echo date('H:i', strtotime($booking_data['departure_time'])); ?></span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">نوع الباص:</span>
                <span class="detail-value"><?php echo $booking_data['bus_type'] == 'vip' ? 'VIP' : 'عادي'; ?></span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">رقم الباص:</span>
                <span class="detail-value"><?php echo htmlspecialchars($booking_data['bus_number']); ?></span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">عدد المقاعد:</span>
                <span class="detail-value"><?php echo $booking_data['seat_count']; ?> <?php echo $booking_data['seat_count'] == 1 ? 'مقعد' : 'مقاعد'; ?></span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">إجمالي المبلغ:</span>
                <span class="detail-value"><?php echo number_format($booking_data['total_price'], 2); ?> ريال</span>
            </div>
        </div>

        <!-- معلومات الدفع -->
        <div class="payment-info">
            <div class="payment-title">💳 معلومات الدفع</div>
            <div class="payment-details">
                <?php if ($booking_data['payment_method'] == 'cash'): ?>
                    <strong>الدفع عند الوصول:</strong><br>
                    يرجى إحضار المبلغ نقداً عند وصولك للمحطة قبل موعد الانطلاق بـ 30 دقيقة على الأقل.
                <?php elseif ($booking_data['payment_method'] == 'bank_transfer'): ?>
                    <strong>التحويل البنكي:</strong><br>
                    سيتم التواصل معك خلال 24 ساعة لإرسال تفاصيل الحساب البنكي للتحويل.
                <?php endif; ?>
            </div>
        </div>

        <!-- ملاحظات مهمة -->
        <div class="important-notes">
            <div class="notes-title">⚠️ ملاحظات مهمة</div>
            <ul class="notes-list">
                <li>يرجى الوصول للمحطة قبل موعد الانطلاق بـ 30 دقيقة على الأقل</li>
                <li>إحضار هوية شخصية سارية المفعول</li>
                <li>في حالة التأخير أو عدم الحضور، لن يتم استرداد المبلغ</li>
                <li>يمكن إلغاء الحجز قبل 24 ساعة من موعد الانطلاق</li>
                <li>للاستفسارات أو التعديل، يرجى التواصل معنا على الرقم المذكور</li>
            </ul>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <a href="<?php echo $Site_URL; ?>/bus-booking" class="btn btn-success">
                🚌 حجز رحلة جديدة
            </a>
            <a href="javascript:window.print()" class="btn btn-primary">
                🖨️ طباعة التذكرة
            </a>
            <a href="<?php echo $Site_URL; ?>/" class="btn btn-secondary">
                🏠 العودة للرئيسية
            </a>
        </div>
    </div>
</div>

<script>
// إضافة وظيفة الطباعة
window.addEventListener('beforeprint', function() {
    document.querySelector('.action-buttons').style.display = 'none';
});

window.addEventListener('afterprint', function() {
    document.querySelector('.action-buttons').style.display = 'flex';
});

// حفظ بيانات الحجز في التخزين المحلي للمراجعة اللاحقة
localStorage.setItem('last_bus_booking', JSON.stringify({
    reference: '<?php echo $booking_data['booking_reference']; ?>',
    date: '<?php echo date('Y-m-d H:i:s'); ?>',
    passenger: '<?php echo htmlspecialchars($booking_data['passenger_name']); ?>',
    phone: '<?php echo htmlspecialchars($booking_data['passenger_phone']); ?>'
}));
</script>

<?php include 'footer.php'; ?>
