-- إدراج بيانات تجريبية لنظام حجز الباصات

-- إدراج المحطات
INSERT INTO `bus_stations` (`id`, `city_id`, `station_name`, `station_address`, `phone`, `status`, `created_at`) VALUES
-- محطات مكة (city_id = 0)
(1, 0, 'محطة الحرم المكي الشريف', 'بجوار الحرم المكي الشريف، مكة المكرمة', '0125551234', 1, NOW()),
(2, 0, 'محطة العزيزية', 'حي العزيزية، مكة المكرمة', '0125551235', 1, NOW()),
(3, 0, 'محطة الشوقية', 'حي الشوقية، مكة المكرمة', '0125551236', 1, NOW()),

-- محطات الرياض (city_id = 1)
(4, 1, 'محطة الرياض المركزية', 'شارع الملك فهد، الرياض', '0115551234', 1, NOW()),
(5, 1, 'محطة الملز', 'حي الملز، الرياض', '0115551235', 1, NOW()),
(6, 1, 'محطة النخيل', 'حي النخيل، الرياض', '0115551236', 1, NOW()),

-- محطات جدة (city_id = 2)
(7, 2, 'محطة جدة المركزية', 'شارع المدينة المنورة، جدة', '0125551237', 1, NOW()),
(8, 2, 'محطة الروضة', 'حي الروضة، جدة', '0125551238', 1, NOW()),
(9, 2, 'محطة البلد', 'جدة البلد، جدة', '0125551239', 1, NOW()),

-- محطات الدمام (city_id = 3)
(10, 3, 'محطة الدمام المركزية', 'شارع الملك سعود، الدمام', '0135551234', 1, NOW()),
(11, 3, 'محطة الفيصلية', 'حي الفيصلية، الدمام', '0135551235', 1, NOW()),

-- محطات المدينة المنورة (city_id = 4)
(12, 4, 'محطة المسجد النبوي', 'بجوار المسجد النبوي الشريف، المدينة المنورة', '0145551234', 1, NOW()),
(13, 4, 'محطة قباء', 'حي قباء، المدينة المنورة', '0145551235', 1, NOW()),

-- محطات الطائف (city_id = 5)
(14, 5, 'محطة الطائف المركزية', 'شارع الملك فيصل، الطائف', '0125551240', 1, NOW()),
(15, 5, 'محطة الحوية', 'حي الحوية، الطائف', '0125551241', 1, NOW());

-- إدراج الطرق (جميع الطرق تنتهي في مكة)
INSERT INTO `bus_routes` (`id`, `route_name`, `from_station_id`, `to_station_id`, `distance_km`, `duration_hours`, `bus_type`, `base_price`, `status`, `created_at`) VALUES
-- من الرياض إلى مكة
(1, 'الرياض - مكة (عادي)', 4, 1, 870, 9, 'regular', 120.00, 1, NOW()),
(2, 'الرياض - مكة (VIP)', 4, 1, 870, 9, 'vip', 180.00, 1, NOW()),
(3, 'الملز - العزيزية', 5, 2, 870, 9, 'regular', 120.00, 1, NOW()),
(4, 'النخيل - الشوقية', 6, 3, 870, 9, 'vip', 180.00, 1, NOW()),

-- من جدة إلى مكة
(5, 'جدة - مكة (عادي)', 7, 1, 79, 1, 'regular', 25.00, 1, NOW()),
(6, 'جدة - مكة (VIP)', 7, 1, 79, 1, 'vip', 40.00, 1, NOW()),
(7, 'الروضة - العزيزية', 8, 2, 79, 1, 'regular', 25.00, 1, NOW()),
(8, 'البلد - الحرم', 9, 1, 79, 1, 'regular', 25.00, 1, NOW()),

-- من الدمام إلى مكة
(9, 'الدمام - مكة (عادي)', 10, 1, 1265, 13, 'regular', 200.00, 1, NOW()),
(10, 'الدمام - مكة (VIP)', 10, 1, 1265, 13, 'vip', 300.00, 1, NOW()),
(11, 'الفيصلية - العزيزية', 11, 2, 1265, 13, 'regular', 200.00, 1, NOW()),

-- من المدينة المنورة إلى مكة
(12, 'المدينة - مكة (عادي)', 12, 1, 358, 4, 'regular', 80.00, 1, NOW()),
(13, 'المدينة - مكة (VIP)', 12, 1, 358, 4, 'vip', 120.00, 1, NOW()),
(14, 'قباء - الشوقية', 13, 3, 358, 4, 'regular', 80.00, 1, NOW()),

-- من الطائف إلى مكة
(15, 'الطائف - مكة (عادي)', 14, 1, 88, 1, 'regular', 30.00, 1, NOW()),
(16, 'الطائف - مكة (VIP)', 14, 1, 88, 1, 'vip', 50.00, 1, NOW()),
(17, 'الحوية - العزيزية', 15, 2, 88, 1, 'regular', 30.00, 1, NOW());

-- إدراج جداول الرحلات (للأسبوع القادم)
INSERT INTO `bus_schedules` (`id`, `route_id`, `departure_date`, `departure_time`, `arrival_time`, `bus_number`, `total_seats`, `available_seats`, `current_price`, `status`, `created_at`) VALUES
-- رحلات اليوم
(1, 1, CURDATE(), '06:00:00', '15:00:00', 'R001', 45, 45, 120.00, 'scheduled', NOW()),
(2, 2, CURDATE(), '07:00:00', '16:00:00', 'R002', 30, 30, 180.00, 'scheduled', NOW()),
(3, 5, CURDATE(), '08:00:00', '09:00:00', 'J001', 45, 45, 25.00, 'scheduled', NOW()),
(4, 6, CURDATE(), '09:00:00', '10:00:00', 'J002', 30, 30, 40.00, 'scheduled', NOW()),
(5, 12, CURDATE(), '10:00:00', '14:00:00', 'M001', 45, 45, 80.00, 'scheduled', NOW()),
(6, 15, CURDATE(), '11:00:00', '12:00:00', 'T001', 45, 45, 30.00, 'scheduled', NOW()),

-- رحلات الغد
(7, 1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '06:00:00', '15:00:00', 'R003', 45, 42, 120.00, 'scheduled', NOW()),
(8, 2, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '07:00:00', '16:00:00', 'R004', 30, 28, 180.00, 'scheduled', NOW()),
(9, 5, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '08:00:00', '09:00:00', 'J003', 45, 40, 25.00, 'scheduled', NOW()),
(10, 6, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:00:00', '10:00:00', 'J004', 30, 25, 40.00, 'scheduled', NOW()),
(11, 9, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '18:00:00', '07:00:00', 'D001', 45, 45, 200.00, 'scheduled', NOW()),
(12, 12, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '10:00:00', '14:00:00', 'M002', 45, 38, 80.00, 'scheduled', NOW()),

-- رحلات بعد غد
(13, 1, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '06:00:00', '15:00:00', 'R005', 45, 45, 120.00, 'scheduled', NOW()),
(14, 2, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '14:00:00', '23:00:00', 'R006', 30, 30, 180.00, 'scheduled', NOW()),
(15, 5, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '08:00:00', '09:00:00', 'J005', 45, 45, 25.00, 'scheduled', NOW()),
(16, 6, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '16:00:00', '17:00:00', 'J006', 30, 30, 40.00, 'scheduled', NOW()),
(17, 10, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '19:00:00', '08:00:00', 'D002', 30, 30, 300.00, 'scheduled', NOW()),
(18, 13, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '11:00:00', '15:00:00', 'M003', 30, 30, 120.00, 'scheduled', NOW()),
(19, 16, DATE_ADD(CURDATE(), INTERVAL 2 DAY), '12:00:00', '13:00:00', 'T002', 30, 30, 50.00, 'scheduled', NOW()),

-- رحلات اليوم الثالث
(20, 3, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '07:00:00', '16:00:00', 'R007', 45, 45, 120.00, 'scheduled', NOW()),
(21, 4, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '15:00:00', '24:00:00', 'R008', 30, 30, 180.00, 'scheduled', NOW()),
(22, 7, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '09:00:00', '10:00:00', 'J007', 45, 45, 25.00, 'scheduled', NOW()),
(23, 8, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '17:00:00', '18:00:00', 'J008', 45, 45, 25.00, 'scheduled', NOW()),
(24, 11, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '20:00:00', '09:00:00', 'D003', 45, 45, 200.00, 'scheduled', NOW()),
(25, 14, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '12:00:00', '16:00:00', 'M004', 45, 45, 80.00, 'scheduled', NOW()),
(26, 17, DATE_ADD(CURDATE(), INTERVAL 3 DAY), '13:00:00', '14:00:00', 'T003', 45, 45, 30.00, 'scheduled', NOW()),

-- رحلات اليوم الرابع
(27, 1, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '06:00:00', '15:00:00', 'R009', 45, 45, 120.00, 'scheduled', NOW()),
(28, 2, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '08:00:00', '17:00:00', 'R010', 30, 30, 180.00, 'scheduled', NOW()),
(29, 5, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '08:00:00', '09:00:00', 'J009', 45, 45, 25.00, 'scheduled', NOW()),
(30, 6, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '18:00:00', '19:00:00', 'J010', 30, 30, 40.00, 'scheduled', NOW()),
(31, 9, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '21:00:00', '10:00:00', 'D004', 45, 45, 200.00, 'scheduled', NOW()),
(32, 12, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '13:00:00', '17:00:00', 'M005', 45, 45, 80.00, 'scheduled', NOW()),
(33, 15, DATE_ADD(CURDATE(), INTERVAL 4 DAY), '14:00:00', '15:00:00', 'T004', 45, 45, 30.00, 'scheduled', NOW()),

-- رحلات اليوم الخامس
(34, 3, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '06:30:00', '15:30:00', 'R011', 45, 45, 120.00, 'scheduled', NOW()),
(35, 4, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '16:00:00', '01:00:00', 'R012', 30, 30, 180.00, 'scheduled', NOW()),
(36, 7, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '08:30:00', '09:30:00', 'J011', 45, 45, 25.00, 'scheduled', NOW()),
(37, 8, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '19:00:00', '20:00:00', 'J012', 45, 45, 25.00, 'scheduled', NOW()),
(38, 10, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '22:00:00', '11:00:00', 'D005', 30, 30, 300.00, 'scheduled', NOW()),
(39, 13, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '14:00:00', '18:00:00', 'M006', 30, 30, 120.00, 'scheduled', NOW()),
(40, 16, DATE_ADD(CURDATE(), INTERVAL 5 DAY), '15:00:00', '16:00:00', 'T005', 30, 30, 50.00, 'scheduled', NOW());

-- إدراج بعض الحجوزات التجريبية
INSERT INTO `bus_bookings` (`id`, `booking_reference`, `schedule_id`, `passenger_name`, `passenger_phone`, `passenger_email`, `passenger_id_number`, `seat_count`, `total_price`, `booking_status`, `payment_status`, `payment_method`, `special_requests`, `created_by_ip`, `created_at`) VALUES
(1, 'BUS20250101001', 7, 'أحمد محمد علي', '**********', '<EMAIL>', '**********', 3, 360.00, 'confirmed', 'pending', 'cash', 'مقاعد متجاورة', '***********', NOW()),
(2, 'BUS20250101002', 9, 'فاطمة سعد الدين', '**********', '<EMAIL>', '**********', 2, 50.00, 'confirmed', 'paid', 'bank_transfer', '', '***********', NOW()),
(3, 'BUS20250101003', 12, 'محمد عبدالله', '**********', '<EMAIL>', '**********', 1, 80.00, 'confirmed', 'pending', 'cash', 'مقعد بجانب النافذة', '***********', NOW());
