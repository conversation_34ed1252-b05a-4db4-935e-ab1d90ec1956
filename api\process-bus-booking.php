<?php
// تضمين ملف الإعدادات
require_once '../webset.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: " . $Site_URL . "/bus-booking");
    exit();
}

// استخراج البيانات
$booking_data = json_decode($_POST['booking_data'], true);
$passenger_name = trim($_POST['passenger_name']);
$passenger_phone = trim($_POST['passenger_phone']);
$passenger_email = trim($_POST['passenger_email']);
$passenger_id = trim($_POST['passenger_id']);
$special_requests = trim($_POST['special_requests']);
$payment_method = $_POST['payment_method'];
$accept_terms = isset($_POST['accept_terms']);

// التحقق من البيانات المطلوبة
if (!$booking_data || !$passenger_name || !$passenger_phone || !$accept_terms) {
    header("Location: " . $Site_URL . "/bus-booking");
    exit();
}

// استخراج بيانات الحجز
$schedule_id = intval($booking_data['schedule_id']);
$seat_count = intval($booking_data['seat_count']);
$total_price = floatval($booking_data['total_price']);

try {
    // بدء المعاملة
    $pdo->beginTransaction();
    
    // التحقق من توفر المقاعد
    $schedule_check = getAllFrom('*', 'bus_schedules', 'WHERE id = "'.$schedule_id.'" AND status = "scheduled"', '');
    
    if (count($schedule_check) == 0) {
        throw new Exception('الرحلة غير متاحة');
    }
    
    $schedule = $schedule_check[0];
    
    if ($schedule['available_seats'] < $seat_count) {
        throw new Exception('عدد المقاعد المطلوبة غير متاح');
    }
    
    // إنشاء رقم مرجعي فريد للحجز
    $booking_reference = 'BUS' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    // التحقق من عدم تكرار الرقم المرجعي
    while (true) {
        $ref_check = getAllFrom('*', 'bus_bookings', 'WHERE booking_reference = "'.$booking_reference.'"', '');
        if (count($ref_check) == 0) {
            break;
        }
        $booking_reference = 'BUS' . date('Ymd') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
    
    // الحصول على عنوان IP
    $user_ip = $_SERVER['REMOTE_ADDR'] ?? '';
    if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $user_ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    }
    
    // إدراج الحجز في قاعدة البيانات
    $insert_booking = "INSERT INTO bus_bookings (
        booking_reference,
        schedule_id,
        passenger_name,
        passenger_phone,
        passenger_email,
        passenger_id_number,
        seat_count,
        total_price,
        booking_status,
        payment_status,
        payment_method,
        special_requests,
        created_by_ip,
        created_at
    ) VALUES (
        '".$booking_reference."',
        '".$schedule_id."',
        '".addslashes($passenger_name)."',
        '".addslashes($passenger_phone)."',
        '".addslashes($passenger_email)."',
        '".addslashes($passenger_id)."',
        '".$seat_count."',
        '".$total_price."',
        'confirmed',
        'pending',
        '".$payment_method."',
        '".addslashes($special_requests)."',
        '".$user_ip."',
        NOW()
    )";
    
    $result = $pdo->exec($insert_booking);
    
    if (!$result) {
        throw new Exception('فشل في حفظ الحجز');
    }
    
    // تحديث عدد المقاعد المتاحة
    $new_available_seats = $schedule['available_seats'] - $seat_count;
    $update_seats = "UPDATE bus_schedules SET available_seats = '".$new_available_seats."' WHERE id = '".$schedule_id."'";
    
    $update_result = $pdo->exec($update_seats);
    
    if (!$update_result) {
        throw new Exception('فشل في تحديث المقاعد المتاحة');
    }
    
    // تأكيد المعاملة
    $pdo->commit();
    
    // إعداد بيانات الحجز للصفحة التالية
    $booking_success_data = [
        'booking_reference' => $booking_reference,
        'passenger_name' => $passenger_name,
        'passenger_phone' => $passenger_phone,
        'schedule_id' => $schedule_id,
        'seat_count' => $seat_count,
        'total_price' => $total_price,
        'payment_method' => $payment_method,
        'from_station_name' => $booking_data['from_station_name'],
        'to_station_name' => $booking_data['to_station_name'],
        'departure_date' => $booking_data['departure_date'],
        'departure_time' => $booking_data['departure_time'],
        'bus_number' => $booking_data['bus_number'],
        'bus_type' => $booking_data['bus_type']
    ];
    
    // التوجه لصفحة نجاح الحجز
    header("Location: " . $Site_URL . "/bus-booking-success/" . urlencode(json_encode($booking_success_data)));
    exit();
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $pdo->rollBack();
    
    // تسجيل الخطأ
    error_log("Bus Booking Error: " . $e->getMessage());
    
    // إعداد رسالة الخطأ
    $error_data = [
        'error' => true,
        'message' => $e->getMessage(),
        'booking_data' => $booking_data
    ];
    
    // التوجه لصفحة الخطأ
    header("Location: " . $Site_URL . "/bus-booking-error/" . urlencode(json_encode($error_data)));
    exit();
}
?>
