<?php
$Title_page = 'خطأ في الحجز';

// فك تشفير بيانات الخطأ
$error_data = json_decode(urldecode($FURL[2]), true);

// التحقق من وجود البيانات
if (!$error_data || !isset($error_data['error'])) {
    header("Location: " . $Site_URL . "/bus-booking");
    exit();
}

$error_message = $error_data['message'] ?? 'حدث خطأ غير متوقع';
$booking_data = $error_data['booking_data'] ?? null;

include 'header.php';
?>

<style>
.error-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.error-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    text-align: center;
}

.error-icon {
    font-size: 80px;
    color: #e74c3c;
    margin-bottom: 20px;
}

.error-title {
    color: #e74c3c;
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 15px;
}

.error-message {
    color: #2c3e50;
    font-size: 18px;
    margin-bottom: 30px;
    line-height: 1.6;
    background: #ffebee;
    padding: 20px;
    border-radius: 8px;
    border-right: 4px solid #e74c3c;
}

.error-suggestions {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    text-align: right;
}

.suggestions-title {
    color: #2c3e50;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
}

.suggestions-list {
    color: #7f8c8d;
    line-height: 1.8;
}

.suggestions-list li {
    margin-bottom: 8px;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #1e8449);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    color: white;
    text-decoration: none;
}

.contact-info {
    background: #e3f2fd;
    border: 1px solid #90caf9;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
}

.contact-title {
    color: #1976d2;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
}

.contact-details {
    color: #1976d2;
    line-height: 1.6;
    text-align: center;
}

@media (max-width: 768px) {
    .error-card {
        padding: 25px;
    }
    
    .error-icon {
        font-size: 60px;
    }
    
    .error-title {
        font-size: 22px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
</style>

<div class="error-container">
    <div class="error-card">
        <div class="error-icon">❌</div>
        <h1 class="error-title">فشل في إتمام الحجز</h1>
        
        <div class="error-message">
            <strong>سبب الخطأ:</strong><br>
            <?php echo htmlspecialchars($error_message); ?>
        </div>

        <div class="error-suggestions">
            <h3 class="suggestions-title">💡 اقتراحات لحل المشكلة</h3>
            <ul class="suggestions-list">
                <?php if (strpos($error_message, 'المقاعد') !== false): ?>
                    <li>قم بتقليل عدد المقاعد المطلوبة</li>
                    <li>جرب البحث عن رحلة في وقت آخر</li>
                    <li>اختر تاريخاً آخر للسفر</li>
                <?php elseif (strpos($error_message, 'الرحلة') !== false): ?>
                    <li>الرحلة قد تكون ألغيت أو انتهت</li>
                    <li>ابحث عن رحلات أخرى متاحة</li>
                    <li>تحقق من التاريخ والوقت المحددين</li>
                <?php else: ?>
                    <li>تأكد من اتصالك بالإنترنت</li>
                    <li>حاول مرة أخرى بعد بضع دقائق</li>
                    <li>تأكد من صحة البيانات المدخلة</li>
                    <li>امسح ذاكرة التخزين المؤقت للمتصفح</li>
                <?php endif; ?>
                <li>إذا استمرت المشكلة، تواصل مع خدمة العملاء</li>
            </ul>
        </div>

        <div class="contact-info">
            <div class="contact-title">📞 تحتاج مساعدة؟</div>
            <div class="contact-details">
                <strong>خدمة العملاء متاحة 24/7</strong><br>
                📱 الهاتف: 920000000<br>
                📧 البريد: <EMAIL><br>
                💬 الواتساب: +966500000000
            </div>
        </div>

        <div class="action-buttons">
            <?php if ($booking_data): ?>
                <a href="<?php echo $Site_URL; ?>/bus-booking-confirm/<?php echo urlencode(json_encode($booking_data)); ?>" class="btn btn-primary">
                    🔄 إعادة المحاولة
                </a>
            <?php endif; ?>
            
            <a href="<?php echo $Site_URL; ?>/bus-booking" class="btn btn-success">
                🔍 بحث جديد
            </a>
            
            <a href="<?php echo $Site_URL; ?>/" class="btn btn-secondary">
                🏠 العودة للرئيسية
            </a>
        </div>
    </div>
</div>

<script>
// تسجيل الخطأ للتحليل (اختياري)
console.error('Bus Booking Error:', {
    message: '<?php echo addslashes($error_message); ?>',
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent
});

// إظهار رسالة تلقائية بعد 5 ثوان
setTimeout(function() {
    if (confirm('هل تريد المحاولة مرة أخرى؟')) {
        <?php if ($booking_data): ?>
            window.location.href = '<?php echo $Site_URL; ?>/bus-booking-confirm/<?php echo urlencode(json_encode($booking_data)); ?>';
        <?php else: ?>
            window.location.href = '<?php echo $Site_URL; ?>/bus-booking';
        <?php endif; ?>
    }
}, 10000); // 10 ثوان
</script>

<?php include 'footer.php'; ?>
